# Business Operations App

This project requires [Node.js][node] v21.4.0 and [Yarn][yarn] v1.22.21

#### Installation

**Node.js** You may install install using [Homebrew][homebrew] or [nvm][nvm].
For Homebrew. `node` can be installed by running the following command.

```shell
$ brew install node@21
```

**Yarn** Yarn can be installed through [Homebrew][homebrew] by running the
following command.

```shell
$ brew install yarn
```

## Scripts

### Root Scripts

- `clean`: Destroys all local dependency
- `dev`: Runs frontend and middleware packages

### Environment variables

Each package includes example file `.env.example`. It specifies `PORT` and other
settings for a project.

### Steps to run development environment

- copy `.env.example` to `.env.local` for frontend and middleware

```
cp packages/frontend/.env.example packages/frontend/.env.local
cp packages/middleware/.env.example packages/middleware/.env.local
```

- install packages and run dev server

```shell
yarn
yarn run dev
```

## Middleware

Sample data is located `/packages/middleware/data/`

## Screenshots

![Screenshot 1](https://github.com/bigappleinsider/project/blob/main/screenshots/Screen1.png?raw=true)

![Screenshot 2](https://github.com/bigappleinsider/project/blob/main/screenshots/Screen2.png?raw=true)

<!-- LINKS -->

[node]: https://nodejs.org/
[yarn]: https://classic.yarnpkg.com/
[homebrew]: https://brew.sh/
[nvm]: https://nvm.sh
