import { Estimate, LineItem, Customer } from '../models';
import {
  EstimateInput,
  EstimateOutput,
  LineItemInput,
  LineItemOutput,
} from '../models/Estimate';

export const getAll = async (): Promise<EstimateOutput[]> => {
  return Estimate.findAll({
    include: [
      {
        model: LineItem,
        as: 'lineItems',
      },
      {
        model: Customer,
        as: 'customer',
        attributes: [
          'id',
          'customerDisplayName',
          'companyName',
          'firstName',
          'lastName',
          'email',
        ],
      },
    ],
    order: [['createdAt', 'DESC']],
  });
};

export const getById = async (id: number): Promise<EstimateOutput | null> => {
  return Estimate.findByPk(id, {
    include: [
      {
        model: LineItem,
        as: 'lineItems',
      },
      {
        model: Customer,
        as: 'customer',
        attributes: [
          'id',
          'customerDisplayName',
          'companyName',
          'firstName',
          'lastName',
          'email',
        ],
      },
    ],
  });
};

export const create = async (
  estimateData: EstimateInput,
  lineItemsData: LineItemInput[],
): Promise<EstimateOutput> => {
  // Calculate totals
  let totalCost = 0;
  let totalPrice = 0;

  const processedLineItems = lineItemsData.map((item) => {
    const units = parseFloat(item.units) || 0;
    const time = parseFloat(item.time || '0') || 0;
    const rate = parseFloat(item.rate.toString()) || 0;
    const margin = parseFloat(item.margin.toString()) || 0;

    let cost = 0;
    if (item.type === 'Labor' || item.type === 'Equipment') {
      cost = units * time * rate;
    } else if (item.type === 'Materials') {
      cost = units * rate;
    }

    const price = margin >= 100 ? cost : cost / (1 - margin / 100);

    totalCost += cost;
    totalPrice += price;

    return {
      ...item,
      cost,
      price,
    };
  });

  // Create estimate with calculated totals
  const estimate = await Estimate.create({
    ...estimateData,
    totalCost,
    totalPrice,
  });

  // Create line items
  const lineItemsWithEstimateId = processedLineItems.map((item) => ({
    ...item,
    estimateId: estimate.id,
  }));

  await LineItem.bulkCreate(lineItemsWithEstimateId);

  // Return the complete estimate with line items
  return getById(estimate.id) as Promise<EstimateOutput>;
};

export const update = async (
  id: number,
  estimateData: Partial<EstimateInput>,
  lineItemsData?: LineItemInput[],
): Promise<EstimateOutput> => {
  const estimate = await Estimate.findByPk(id);
  if (!estimate) {
    throw new Error('Estimate not found');
  }

  // If line items are provided, recalculate totals
  if (lineItemsData) {
    let totalCost = 0;
    let totalPrice = 0;

    const processedLineItems = lineItemsData.map((item) => {
      const units = parseFloat(item.units) || 0;
      const time = parseFloat(item.time || '0') || 0;
      const rate = parseFloat(item.rate.toString()) || 0;
      const margin = parseFloat(item.margin.toString()) || 0;

      let cost = 0;
      if (item.type === 'Labor' || item.type === 'Equipment') {
        cost = units * time * rate;
      } else if (item.type === 'Materials') {
        cost = units * rate;
      }

      const price = margin >= 100 ? cost : cost / (1 - margin / 100);

      totalCost += cost;
      totalPrice += price;

      return {
        ...item,
        cost,
        price,
        estimateId: id,
      };
    });

    // Delete existing line items and create new ones
    await LineItem.destroy({ where: { estimateId: id } });
    await LineItem.bulkCreate(processedLineItems);

    // Update estimate with new totals
    await estimate.update({
      ...estimateData,
      totalCost,
      totalPrice,
    });
  } else {
    // Just update estimate data
    await estimate.update(estimateData);
  }

  return getById(id) as Promise<EstimateOutput>;
};

export const deleteById = async (id: number): Promise<boolean> => {
  const estimate = await Estimate.findByPk(id);
  if (!estimate) {
    throw new Error('Estimate not found');
  }

  // Delete line items first (cascade)
  await LineItem.destroy({ where: { estimateId: id } });

  // Delete estimate
  await estimate.destroy();

  return true;
};
