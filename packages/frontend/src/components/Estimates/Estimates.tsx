import { Helmet } from 'react-helmet-async';
import TableHeader from '../TableHeader';
import { useNavigate } from 'react-router-dom';

export default function Estimates() {
  const navigate = useNavigate();

  return (
    <div>
      <Helmet>
        <title>Estimates | Project</title>
      </Helmet>
      {/* <Breadcrumbs /> */}

      <TableHeader
        label="Estimates"
        createLabel="New Estimate"
        onCreate={() => {
          navigate('/estimates/new');
        }}
      />

      {/* <WorkspaceTable /> */}
    </div>
  );
}
