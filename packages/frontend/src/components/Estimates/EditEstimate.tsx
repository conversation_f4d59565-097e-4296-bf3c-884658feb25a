import { useNavigate, useParams } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import { Box, Typography } from '@mui/material';
import EstimateForm from './EstimateForm';
import { useEstimate, useUpdateEstimate } from './hooks';
import { LineItem } from '../../interfaces';
import Loader from '../Loader';
import ErrorDisplay from '../ErrorDisplay';

export default function EditEstimate() {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const estimateId = parseInt(id || '0', 10);

  const { data: estimate, isLoading, isError } = useEstimate(estimateId);
  const updateEstimateMutation = useUpdateEstimate();

  const handleSubmit = async (values: any) => {
    try {
      // Filter out line items with empty types and transform data
      const validLineItems = values.lineItems
        .filter((item: any) => item.type && item.type !== '')
        .map((item: any) => ({
          type: item.type as 'Labor' | 'Materials' | 'Equipment',
          item: item.item,
          units: item.units,
          time: item.time || undefined,
          rate: item.rate,
          margin: item.margin,
        }));

      if (validLineItems.length === 0) {
        alert('Please add at least one valid line item.');
        return;
      }

      const payload = {
        estimate: {
          name: estimate?.name || `Estimate ${estimateId}`,
          description: estimate?.description || 'Updated estimate',
          customerId: values.customerId,
        },
        lineItems: validLineItems,
      };

      await updateEstimateMutation.mutateAsync({
        id: estimateId,
        payload,
      });

      navigate('/estimates');
    } catch (error) {
      console.error('Error updating estimate:', error);
    }
  };

  if (isLoading) return <Loader />;

  if (isError || !estimate) {
    return <ErrorDisplay />;
  }

  // Transform estimate data to form format
  const initialValues = {
    customerId: estimate.customerId || null,
    lineItems: estimate.lineItems?.map((item: LineItem) => ({
      type: item.type,
      item: item.item,
      units: item.units,
      time: item.time || '',
      rate: item.rate.toString(),
      margin: item.margin.toString(),
    })) || [
      {
        type: '',
        item: '',
        units: '',
        time: '',
        rate: '',
        margin: '',
      },
    ],
  };

  return (
    <Box margin="20px">
      <Helmet>
        <title>Edit Estimate | Project</title>
      </Helmet>

      <Box marginBottom="20px">
        <Typography variant="h4" gutterBottom>
          Estimate {id}
        </Typography>
        <Typography variant="body1" color="textSecondary">
          {estimate.name || `Estimate ${estimateId}`}
        </Typography>
      </Box>

      <EstimateForm
        onSubmit={handleSubmit}
        initialValues={initialValues}
        isLoading={updateEstimateMutation.isLoading}
      />
    </Box>
  );
}
