import {
  Box,
  Typography,
  Paper,
  Divider,
  CircularProgress,
} from '@mui/material';
import { useCustomer } from '../Customers/hooks';

interface BillToDisplayProps {
  customerId: number | null;
}

export default function BillToDisplay({ customerId }: BillToDisplayProps) {
  const { data: customer, isLoading, isError } = useCustomer(customerId);

  if (!customerId) {
    return null;
  }

  if (isLoading) {
    return (
      <Paper
        variant="outlined"
        sx={{
          padding: 2,
          backgroundColor: '#f9f9f9',
          border: '1px solid #e0e0e0',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: 100,
        }}
      >
        <CircularProgress size={24} />
      </Paper>
    );
  }

  if (isError || !customer) {
    return (
      <Paper
        variant="outlined"
        sx={{
          padding: 2,
          backgroundColor: '#f9f9f9',
          border: '1px solid #e0e0e0',
        }}
      >
        <Typography
          variant="h6"
          gutterBottom
          sx={{ color: '#666', fontSize: '0.9rem', fontWeight: 600 }}
        >
          BILL TO
        </Typography>
        <Divider sx={{ marginBottom: 1.5 }} />
        <Typography variant="body2" color="error">
          Error loading customer information
        </Typography>
      </Paper>
    );
  }

  const getCustomerDisplayName = (): string => {
    if (customer.customerDisplayName) {
      return customer.customerDisplayName;
    }
    if (customer.companyName) {
      return customer.companyName;
    }
    const nameParts = [customer.firstName, customer.lastName].filter(Boolean);
    return nameParts.length > 0
      ? nameParts.join(' ')
      : `Customer ${customer.id}`;
  };

  const formatAddress = (): string[] => {
    const addressLines: string[] = [];

    if (customer.billingStreetAddress1) {
      addressLines.push(customer.billingStreetAddress1);
    }

    if (customer.billingStreetAddress2) {
      addressLines.push(customer.billingStreetAddress2);
    }

    // City, State ZIP format
    const cityStateZip = [
      customer.billingCity,
      customer.billingState,
      customer.billingZipCode,
    ]
      .filter(Boolean)
      .join(', ');

    if (cityStateZip) {
      addressLines.push(cityStateZip);
    }

    if (
      customer.billingCountry &&
      customer.billingCountry !== 'US' &&
      customer.billingCountry !== 'USA'
    ) {
      addressLines.push(customer.billingCountry);
    }

    return addressLines;
  };

  const addressLines = formatAddress();
  const hasAddress = addressLines.length > 0;

  return (
    <Paper
      variant="outlined"
      sx={{
        padding: 2,
        backgroundColor: '#f9f9f9',
        border: '1px solid #e0e0e0',
      }}
    >
      <Typography
        variant="h6"
        gutterBottom
        sx={{ color: '#666', fontSize: '0.9rem', fontWeight: 600 }}
      >
        BILL TO
      </Typography>

      <Divider sx={{ marginBottom: 1.5 }} />

      <Box>
        <Typography variant="body1" sx={{ fontWeight: 500, marginBottom: 0.5 }}>
          {getCustomerDisplayName()}
        </Typography>

        {customer.email && (
          <Typography
            variant="body2"
            color="textSecondary"
            sx={{ marginBottom: 0.5 }}
          >
            {customer.email}
          </Typography>
        )}

        {customer.phoneNumber && (
          <Typography
            variant="body2"
            color="textSecondary"
            sx={{ marginBottom: 0.5 }}
          >
            {customer.phoneNumber}
          </Typography>
        )}

        {hasAddress && (
          <Box sx={{ marginTop: 1 }}>
            {addressLines.map((line, index) => (
              <Typography key={index} variant="body2" color="textSecondary">
                {line}
              </Typography>
            ))}
          </Box>
        )}

        {!hasAddress && (
          <Typography
            variant="body2"
            color="textSecondary"
            sx={{ fontStyle: 'italic', marginTop: 1 }}
          >
            No billing address on file
          </Typography>
        )}
      </Box>
    </Paper>
  );
}
