import { useMutation, useQuery, useQueryClient } from 'react-query';
import axios from 'axios';

const API_BASE_URL = 'http://localhost:3000/api/v1';

// Types
export interface Customer {
  id: number;
  // Name and contact fields
  title?: string;
  firstName?: string;
  middleName?: string;
  lastName?: string;
  suffix?: string;
  companyName?: string;
  customerDisplayName?: string;
  email?: string;
  phoneNumber?: string;
  mobileNumber?: string;
  fax?: string;
  other?: string;
  website?: string;
  nameToPrintOnChecks?: string;
  
  // Billing address fields
  billingStreetAddress1?: string;
  billingStreetAddress2?: string;
  billingCity?: string;
  billingState?: string;
  billingZipCode?: string;
  billingCountry?: string;
  
  // Shipping address fields
  shippingStreetAddress1?: string;
  shippingStreetAddress2?: string;
  shippingCity?: string;
  shippingState?: string;
  shippingZipCode?: string;
  shippingCountry?: string;
  sameAsBillingAddress: boolean;
  
  createdAt: string;
  updatedAt: string;
}

export interface CustomerInput extends Omit<Customer, 'id' | 'createdAt' | 'updatedAt'> {}

// API functions
const customerApi = {
  getAll: async (search?: string): Promise<Customer[]> => {
    const params = search ? { search } : {};
    const response = await axios.get(`${API_BASE_URL}/customers`, { params });
    return response.data;
  },

  getById: async (id: number): Promise<Customer> => {
    const response = await axios.get(`${API_BASE_URL}/customers/${id}`);
    return response.data;
  },

  create: async (data: CustomerInput): Promise<Customer> => {
    const response = await axios.post(`${API_BASE_URL}/customers`, data);
    return response.data;
  },

  update: async (id: number, data: Partial<CustomerInput>): Promise<Customer> => {
    const response = await axios.put(`${API_BASE_URL}/customers/${id}`, data);
    return response.data;
  },

  delete: async (id: number): Promise<void> => {
    await axios.delete(`${API_BASE_URL}/customers/${id}`);
  },
};

// React Query hooks
export const useCustomers = (search?: string) => {
  return useQuery(['customers', search], () => customerApi.getAll(search), {
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useCustomer = (id: number) => {
  return useQuery(['customers', id], () => customerApi.getById(id), {
    enabled: !!id,
  });
};

export const useCreateCustomer = () => {
  const queryClient = useQueryClient();
  
  return useMutation(customerApi.create, {
    onSuccess: () => {
      queryClient.invalidateQueries(['customers']);
    },
  });
};

export const useUpdateCustomer = () => {
  const queryClient = useQueryClient();
  
  return useMutation(
    ({ id, data }: { id: number; data: Partial<CustomerInput> }) =>
      customerApi.update(id, data),
    {
      onSuccess: (data) => {
        queryClient.invalidateQueries(['customers']);
        queryClient.setQueryData(['customers', data.id], data);
      },
    }
  );
};

export const useDeleteCustomer = () => {
  const queryClient = useQueryClient();
  
  return useMutation(customerApi.delete, {
    onSuccess: () => {
      queryClient.invalidateQueries(['customers']);
    },
  });
};
