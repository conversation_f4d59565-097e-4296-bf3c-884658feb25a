import queryString from 'query-string';
import { HttpMethods, fetchWithHeaders } from '../utilities/network';

interface GetPaginatedGroupingsParams {
  page: number;
  limit?: number;
  order: 'asc' | 'desc';
  orderBy: string;
}

const DEFAULT_PAGE_SIZE = 10;

export default class ProjectClient {
  prefix = '/api/v1';
  private apiEndpoint: string;

  constructor(apiEndpoint: string) {
    this.apiEndpoint = apiEndpoint;
  }

  async getGroupings() {
    return fetchWithHeaders(`${this.apiEndpoint}${this.prefix}/groupings`, {
      method: HttpMethods.Get,
    });
  }

  async getGroupingsPaginate({
    page,
    limit = DEFAULT_PAGE_SIZE,
    order,
    orderBy,
  }: GetPaginatedGroupingsParams) {
    const queryStringRes = queryString.stringify({
      page,
      limit,
      order,
      orderBy,
    });
    return fetchWithHeaders(
      `${this.apiEndpoint}${this.prefix}/groupings/paginate?${queryStringRes}`,
      {
        method: HttpMethods.Get,
      },
    );
  }

  async getGroupingsSeverityCounts() {
    return fetchWithHeaders(
      `${this.apiEndpoint}${this.prefix}/groupings/severity/counts`,
      {
        method: HttpMethods.Get,
      },
    );
  }

  async getFindingsByGroup(groupedFindingId: number) {
    return fetchWithHeaders(
      `${this.apiEndpoint}${this.prefix}/findings/${groupedFindingId}`,
      {
        method: HttpMethods.Get,
      },
    );
  }

  async createFinding(payload: any) {
    return fetchWithHeaders(`${this.apiEndpoint}${this.prefix}/findings`, {
      method: HttpMethods.Post,
    });
  }

  async updateGrouping(id: string | number, payload: any) {
    return fetchWithHeaders(
      `${this.apiEndpoint}${this.prefix}/groupings/${id}`,
      {
        method: HttpMethods.Patch,
        body: JSON.stringify(payload),
      },
    );
  }

  async createEstimate(payload: any) {
    return fetchWithHeaders(`${this.apiEndpoint}${this.prefix}/estimates`, {
      method: HttpMethods.Post,
      body: JSON.stringify(payload),
    });
  }

  async getEstimates() {
    return fetchWithHeaders(`${this.apiEndpoint}${this.prefix}/estimates`, {
      method: HttpMethods.Get,
    });
  }

  async getEstimate(id: number) {
    return fetchWithHeaders(
      `${this.apiEndpoint}${this.prefix}/estimates/${id}`,
      {
        method: HttpMethods.Get,
      },
    );
  }

  async updateEstimate(id: number, payload: any) {
    return fetchWithHeaders(
      `${this.apiEndpoint}${this.prefix}/estimates/${id}`,
      {
        method: HttpMethods.Put,
        body: JSON.stringify(payload),
      },
    );
  }

  async deleteEstimate(id: number) {
    return fetchWithHeaders(
      `${this.apiEndpoint}${this.prefix}/estimates/${id}`,
      {
        method: HttpMethods.Delete,
      },
    );
  }

  async getCustomers() {
    return fetchWithHeaders(`${this.apiEndpoint}${this.prefix}/customers`, {
      method: HttpMethods.Get,
    });
  }

  async getCustomer(id: number) {
    return fetchWithHeaders(
      `${this.apiEndpoint}${this.prefix}/customers/${id}`,
      {
        method: HttpMethods.Get,
      },
    );
  }

  async createCustomer(payload: any) {
    return fetchWithHeaders(`${this.apiEndpoint}${this.prefix}/customers`, {
      method: HttpMethods.Post,
      body: JSON.stringify(payload),
    });
  }

  async updateCustomer(id: number, payload: any) {
    return fetchWithHeaders(
      `${this.apiEndpoint}${this.prefix}/customers/${id}`,
      {
        method: HttpMethods.Put,
        body: JSON.stringify(payload),
      },
    );
  }

  async deleteCustomer(id: number) {
    return fetchWithHeaders(
      `${this.apiEndpoint}${this.prefix}/customers/${id}`,
      {
        method: HttpMethods.Delete,
      },
    );
  }
}
