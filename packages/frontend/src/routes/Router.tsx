import React from 'react';
import { Route, Routes } from 'react-router-dom';
import Dashboard from './Dashboard';
import Estimates from '../components/Estimates';
import Layout from '../components/Layout';
import NewEstimate from '../components/Estimates/NewEstimate';

export default function Router() {
  return (
    <Routes>
      <Route path="/" element={<Layout />}>
        <Route index element={<Dashboard />} />
        <Route path="/estimates" element={<Estimates />} />
      </Route>
      <Route path="/estimates/new" element={<NewEstimate />} />
    </Routes>
  );
}
